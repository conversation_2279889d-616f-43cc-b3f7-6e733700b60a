import { ApiResponsePagination } from '@app/shared/decorators/openapi.decorator';
import {
  ReadOnlyInterceptor,
  ResponseInterceptor,
} from '@app/shared/interceptors';
import { JwtAccessTokenGuard } from '@app/auth/guards/jwt-access-token.guard';
import {
  Body,
  ConflictException,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  HttpCode,
  HttpStatus,
  Inject,
  NotAcceptableException,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Req,
  Res,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiConflictResponse,
  ApiCookieAuth,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiNotAcceptableResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  ResponseErrorDto,
  ResponseNotFoundDto,
  ResponseOkDto,
  ResponseConflictDto,
  ResponseNotAcceptableDto,
} from '@app/shared/dto';
import { StoreMessageDto } from '@app/conversation/dto/store-message.dto';
import { CheckAvailableConversationGuard } from '@app/conversation/guards/check-available-conversation.guard';
import { CheckAvailableMessageGuard } from '@app/conversation/guards/check-available-message.guard';
import { EditMessageDto } from '@app/conversation/dto/edit-message.dto';
import { MessageDto } from '@app/conversation/dto/message.dto';
import { MessageReactionService } from '@app/conversation/services/message-reaction.service';
import { MessageService } from '@app/conversation/services/message.service';
import {
  ConversationMember,
  Message,
  MessageAttachment,
  User,
} from '@app/shared/database/entities';
import { RequestWithUser } from '@app/shared/types/requests.type';
import { ReactDto } from '@app/conversation/dto/react.dto';
import { MessageReactionDto } from '@app/conversation/dto/message-reaction.dto';
import { SERVER_MEMBER_TYPE } from '@app/community/server/constants';
import { UnreactDto } from '@app/conversation/dto/unreact.dto';
import moment from 'moment';
import { PinnedMessageDto } from '@app/conversation/dto/pinned-message.dto';
import { AttachmentDto } from '@app/conversation/dto/attachment.dto';
import { MetadataDto } from '@app/conversation/dto/metadata.dto';
import { ThumbnailDto } from '@app/conversation/dto/thumbnail.dto';
import { DeleteMessageDto } from '@app/conversation/dto/delete-message.dto';
import { UnPinMessageDto } from '@app/conversation/dto/unpin-message.dto';
import { ConversationMemberService } from '@app/conversation/services/conversation-member.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { MessageUpdatedEvent } from '@app/conversation/events/message-updated.event';
import { MessageCreatedEvent } from '@app/conversation/events/message-created.event';
import { DeleteAttachmentDto } from '@app/conversation/dto/delete-attachment.dto';
import {
  MESSAGE_EMBED_EVENT_TYPE,
  MESSAGE_EVENT_TYPE,
  MESSAGE_REACTION_EVENT_TYPE,
  MESSAGE_TYPE,
  MESSAGE_STATUS,
} from '@app/conversation/constants';
import { S3Service } from '@app/aws/services/s3.service';
import { Response } from 'express';
import { UrlMetaDto } from '@app/community/server/dto/url-meta.dto';
import { OgpService } from '@app/conversation/services/ogp.service';
import { MessageReactionCreatedEvent } from '@app/conversation/events/message-reaction-created.event';
import { MessageNotificationService } from '@app/conversation/services/message-notification.service';
import { UpdateReadTimeDto } from '@app/conversation/dto/update-read-time.dto';
import { extractUrls } from '@app/shared/helpers/url.helper';
import {
  ConversationMemberRepositoryInterface,
  ConversationRepositoryInterface,
  MessageAttachmentRepositoryInterface,
  MessageEmbedRepositoryInterface,
  MessageRepositoryInterface,
  PinnedMessageRepositoryInterface,
} from '@app/shared/database/repositories';
import { MessageReactionDeletedEvent } from '@app/conversation/events/message-reaction-deleted.event';
import { RetrieveReadTimeDto } from '@app/conversation/dto/retrieve-read-time.dto';
import { GetChatHistoryQueryDto } from '@app/conversation/dto/get-chat-history-query.dto';
import { CheckConversationMemberGuard } from '@app/conversation/guards/check-conversation-member.guard';
import { FindOptionsWhere, MoreThan } from 'typeorm';
import { CheckConversationPublicGuard } from '@app/conversation/guards/check-conversation-public.guard';
import { plainToInstance } from 'class-transformer';
import { v4 } from 'uuid';
import { CheckOwnerConversationGuard } from '@app/conversation/guards/check-owner-conversation.guard';
import { CheckUpdateMessageEmbedGuard } from '@app/conversation/guards/check-update-message-embed.guard';
import { MessageEmbedDeletedEvent } from '@app/conversation/events/message-embed.event';
import { GeneratePresignedUrlDto } from '@app/conversation/dto/generate-presigned-url.dto';
import { PresignedUrlDto } from '@app/conversation/dto/presigned-url.dto';
import { isEmpty } from 'lodash';
import { CheckAccessibleConversationGuard } from '@app/conversation/guards/check-accessible-conversation.guard';
import { Public } from '@app/shared/decorators/auth.decorator';
import { DB_SERVICE_NAME } from '@app/shared/database/constants';
import { CheckReactableConversationGuard } from '@app/conversation/guards/check-reactable-conversation-guard.service';
import { UnreadNotificationQueryDto } from '@app/conversation/dto/unread-notification-query.dto';
import { UnreadNotificationDto } from '@app/conversation/dto/unread-notification.dto';
import { UnreadMessageDto } from '@app/conversation/dto/unread-message.dto';

@UseGuards(JwtAccessTokenGuard)
@ApiBearerAuth('Access token')
@ApiCookieAuth()
@ApiTags('Conversation')
@ApiExtraModels(
  ResponseOkDto,
  MessageDto,
  MessageReactionDto,
  AttachmentDto,
  MetadataDto,
  ThumbnailDto,
  PinnedMessageDto,
  UrlMetaDto,
  UnreadNotificationDto,
  UnreadMessageDto,
)
@ApiNotFoundResponse({
  type: ResponseNotFoundDto,
})
@ApiNotAcceptableResponse({
  type: ResponseNotAcceptableDto,
})
@ApiConflictResponse({
  type: ResponseConflictDto,
})
@ApiInternalServerErrorResponse({
  type: ResponseErrorDto,
})
@Controller('conversations')
export class ConversationController {
  constructor(
    @Inject('ConversationRepositoryInterface')
    private readonly conversationRepository: ConversationRepositoryInterface,
    @Inject('ConversationMemberRepositoryInterface')
    private readonly conversationMemberRepository: ConversationMemberRepositoryInterface,
    @Inject('MessageAttachmentRepositoryInterface')
    private readonly messageAttachmentRepository: MessageAttachmentRepositoryInterface,
    @Inject('PinnedMessageRepositoryInterface')
    private readonly pinnedMessageRepository: PinnedMessageRepositoryInterface,
    @Inject('MessageRepositoryInterface')
    private readonly messageRepository: MessageRepositoryInterface,
    @Inject('MessageEmbedRepositoryInterface')
    private readonly messageEmbedRepository: MessageEmbedRepositoryInterface,
    private readonly conversationMemberService: ConversationMemberService,
    private readonly messageService: MessageService,
    private readonly messageReactionService: MessageReactionService,
    private readonly messageNotificationService: MessageNotificationService,
    private readonly ogpService: OgpService,
    private readonly s3Service: S3Service,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  @Public()
  @Get(':conversationId/messages')
  @ApiOperation({
    summary: 'Get chat history in conversation',
  })
  @ApiResponsePagination(MessageDto)
  @UseGuards(CheckAvailableConversationGuard, CheckAccessibleConversationGuard)
  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.CONVERSATION))
  @UseInterceptors(new ResponseInterceptor(MessageDto))
  async getChatHistory(
    @Param('conversationId') conversationId: string,
    @Query() query: GetChatHistoryQueryDto,
    @Req() request: RequestWithUser,
  ) {
    const { skip, limit, before, after, parentId, replyThread, hasUnread } =
      query;

    let around = query.around;

    const userId = request?.user?.id || '';

    const conversationMember = userId
      ? await this.conversationMemberRepository.findOneBy({
          conversationId,
          userId,
        })
      : ({} as ConversationMember);

    const baseCondition: FindOptionsWhere<Message> = {
      conversationId,
      ...(parentId ? { parentId } : {}),
      ...(replyThread ? { replyThread } : {}),
    };

    if (hasUnread) {
      const firstUnreadMessage = await this.messageRepository.findOne({
        select: {
          id: true,
        },
        where: {
          ...baseCondition,
          conversationId,
          createdAt: MoreThan(conversationMember.lastReadTime),
        },
        order: {
          createdAt: 'ASC',
        },
      });

      if (firstUnreadMessage) {
        around = firstUnreadMessage.id;
      }
    }

    let messages = [];
    let total = 0;
    if (around) {
      const [items, count] = await this.messageService.findManyByAroundId(
        around,
        {
          where: baseCondition,
          take: limit,
        },
        conversationMember,
      );
      messages = items;
      total = count;
    } else if (before) {
      const [items, count] = await this.messageService.findManyByBeforeId(
        before,
        { where: baseCondition, take: limit },
        conversationMember,
      );
      messages = items;
      total = count;
    } else if (after) {
      const [items, count] = await this.messageService.findManyByAfterId(
        after,
        { where: baseCondition, take: limit },
        conversationMember,
      );
      messages = items;
      total = count;
    } else {
      const [items, count] = await this.messageRepository.findAndCount({
        select: {
          id: true,
          conversationId: true,
          senderId: true,
          parentId: true,
          type: true,
          content: true,
          replyThread: true,
          status: true,
          createdAt: true,
          updatedAt: true,
        },
        where: !isEmpty(conversationMember)
          ? [
              {
                ...baseCondition,
                status: MESSAGE_STATUS.PUBLISHED,
              },
              {
                ...baseCondition,
                status: MESSAGE_STATUS.DRAFTED,
                ...(conversationMember?.isOwner
                  ? {}
                  : { senderId: request.user.id }),
              },
            ]
          : {
              ...baseCondition,
              status: MESSAGE_STATUS.PUBLISHED,
            },
        take: limit,
        skip: skip ?? 0,
        order: {
          createdAt: 'DESC',
        },
      });
      messages = items;
      total = count;
    }

    return {
      items: await this.messageService.processMessagesHistory(
        messages,
        conversationMember,
      ),
      count: total,
    };
  }

  @Get(':conversationId/messages/:messageId')
  @ApiOperation({
    summary: 'Get message detail',
  })
  @ApiOkResponse({
    schema: {
      allOf: [
        { $ref: getSchemaPath(ResponseOkDto) },
        {
          properties: {
            data: {
              $ref: getSchemaPath(MessageDto),
            },
          },
        },
      ],
    },
  })
  @UseGuards(
    CheckAvailableConversationGuard,
    CheckConversationMemberGuard,
    CheckAvailableMessageGuard,
  )
  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.CONVERSATION))
  @UseInterceptors(new ResponseInterceptor(MessageDto))
  async getMessageDetail(
    @Param('conversationId') conversationId: string,
    @Param('messageId') messageId: string,
  ): Promise<MessageDto> {
    const message: Message =
      await this.messageRepository.findOneById(messageId);
    if (conversationId !== message?.conversationId) {
      throw new NotAcceptableException({
        code: 'MESSAGE_NOT_BELONGS_TO_CONVERSATION',
        message: 'Message is not belongs to this conversation',
      });
    }
    return await this.messageService.processMessageHistory(message);
  }

  @Post(':conversationId/messages')
  @ApiOperation({
    summary: 'Store message in conversation',
  })
  @ApiCreatedResponse({
    schema: {
      allOf: [
        { $ref: getSchemaPath(ResponseOkDto) },
        {
          properties: {
            data: {
              $ref: getSchemaPath(MessageDto),
            },
          },
        },
      ],
    },
  })
  @ApiConflictResponse({
    type: ResponseConflictDto,
  })
  @ApiInternalServerErrorResponse({
    type: ResponseErrorDto,
  })
  @UseGuards(
    CheckAvailableConversationGuard,
    CheckConversationMemberGuard,
    CheckConversationPublicGuard,
  )
  @UseInterceptors(new ResponseInterceptor(MessageDto))
  async storeMessage(
    @Param('conversationId') conversationId: string,
    @Req() request: RequestWithUser,
    @Body() input: StoreMessageDto,
  ): Promise<MessageDto | boolean> {
    const user: User = request.user;
    input.senderId = user.id;

    input.conversationId = conversationId;
    const isValidMessage: boolean = await this.messageService.isValidMessage(
      input.content,
      input.conversationId,
    );
    if (input?.isCheckedNGWord && isValidMessage) {
      return true;
    }

    const conversationMember =
      await this.conversationMemberRepository.findOneBy({
        conversationId,
        userId: user.id,
      });

    if (conversationMember.bannedAt && isEmpty(input.scheduleAt)) {
      input.status = MESSAGE_STATUS.DRAFTED;
    }

    const message = await this.messageService.processMessageHistory(
      await this.messageService.createMessage(input),
      {
        userId: user.id,
      },
    );

    this.eventEmitter.emit(
      MESSAGE_EVENT_TYPE.CREATED,
      new MessageCreatedEvent(
        {
          ...input,
          ...(message ?? {}),
        } as MessageDto,
        input?.clientId,
      ),
    );

    return message;
  }

  @Put(':conversationId/messages/:messageId')
  @ApiOperation({
    summary: 'Edit message in conversation',
  })
  @ApiOkResponse({
    schema: {
      allOf: [
        { $ref: getSchemaPath(ResponseOkDto) },
        {
          properties: {
            data: {
              $ref: getSchemaPath(MessageDto),
            },
          },
        },
      ],
    },
  })
  @UseGuards(
    CheckAvailableConversationGuard,
    CheckConversationMemberGuard,
    CheckConversationPublicGuard,
    CheckAvailableMessageGuard,
  )
  @UseInterceptors(new ResponseInterceptor(MessageDto))
  async editMessage(
    @Param('conversationId') conversationId: string,
    @Param('messageId') messageId: string,
    @Req() request: RequestWithUser,
    @Body() input: EditMessageDto,
  ): Promise<MessageDto> {
    const userId: string = request.user.id;
    const message: Message =
      await this.messageRepository.findOneById(messageId);
    if (
      message.status == MESSAGE_STATUS.SCHEDULED &&
      input.status != MESSAGE_STATUS.SCHEDULED
    ) {
      input.createdAt = new Date();
    }
    if (conversationId !== message?.conversationId) {
      throw new ConflictException({
        code: 'MESSAGE_NOT_BELONGS_TO_CONVERSATION',
        message: 'Message is not belongs to this conversation',
      });
    }
    if (userId !== message?.senderId) {
      throw new ConflictException({
        code: 'CANNOT_EDIT_MESSAGE_OF_OTHER_USER',
        message: 'Cannot edit message of other user',
      });
    }

    const conversationMember =
      await this.conversationMemberRepository.findOneBy({
        conversationId,
        userId,
      });

    if (
      conversationMember.bannedAt &&
      message.status === MESSAGE_STATUS.PUBLISHED
    ) {
      throw new ForbiddenException({
        code: 'CANNOT_EDIT_MESSAGE',
        message: 'Can not edit message',
      });
    }
    const statusOld = message.status;
    if (
      conversationMember.bannedAt &&
      statusOld == MESSAGE_STATUS.SCHEDULED &&
      input.status != MESSAGE_STATUS.SCHEDULED
    ) {
      input.status = MESSAGE_STATUS.DRAFTED;
    }

    const messageEdit = await this.messageService.edit(messageId, input);

    const messageDto = await this.messageService.processMessageHistory(
      {
        ...messageEdit,
        ...input,
      } as Message,
      {
        userId,
      },
    );
    if (
      messageDto.status == MESSAGE_STATUS.PUBLISHED ||
      messageDto.status == MESSAGE_STATUS.DRAFTED
    ) {
      if (
        statusOld == MESSAGE_STATUS.SCHEDULED &&
        messageDto.status == MESSAGE_STATUS.PUBLISHED
      ) {
        this.eventEmitter.emit(
          MESSAGE_EVENT_TYPE.CREATED,
          new MessageCreatedEvent(messageDto, input?.clientId),
        );
      } else {
        this.eventEmitter.emit(
          MESSAGE_EVENT_TYPE.UPDATED,
          new MessageUpdatedEvent(messageDto, input?.clientId),
        );
      }
    }

    return messageDto;
  }

  @Delete(':conversationId/messages/:messageId')
  @ApiOperation({
    summary: 'Delete message in conversation',
  })
  @ApiOkResponse({
    type: ResponseOkDto,
  })
  @UseGuards(
    CheckAvailableConversationGuard,
    CheckConversationMemberGuard,
    CheckConversationPublicGuard,
    CheckAvailableMessageGuard,
  )
  @UseInterceptors(new ResponseInterceptor(ResponseOkDto))
  async deleteMessage(
    @Param('conversationId') conversationId: string,
    @Param('messageId') messageId: string,
    @Req() request: RequestWithUser,
    @Body() input: DeleteMessageDto,
  ): Promise<boolean> {
    const userId: string = request.user.id;

    const conversationMember =
      await this.conversationMemberRepository.findOneBy({
        conversationId,
        userId,
      });

    const isOwner = conversationMember.isOwner === SERVER_MEMBER_TYPE.OWNER;

    const message: Message =
      await this.messageRepository.findOneById(messageId);
    if (conversationId !== message?.conversationId) {
      throw new ConflictException({
        code: 'MESSAGE_NOT_BELONGS_TO_CONVERSATION',
        message: 'Message is not belongs to this conversation',
      });
    }
    if (message.type === MESSAGE_TYPE.PIN && !isOwner) {
      throw new ConflictException({
        code: 'ONLY_OWNER_CAN_DELETE_PINNED_MESSAGE',
        message: 'Only owner can delete pinned message',
      });
    }
    if (!isOwner && userId !== message?.senderId) {
      throw new ConflictException({
        code: 'CANNOT_DELETE_MESSAGE_OF_OTHER_USER',
        message: 'Cannot delete message of other user',
      });
    }

    if (
      !isOwner &&
      conversationMember.bannedAt &&
      message.status === MESSAGE_STATUS.PUBLISHED
    ) {
      throw new ForbiddenException({
        code: 'CANNOT_DELETE_MESSAGE',
        message: 'Can not delete message',
      });
    }

    await this.messageService.delete(message, input?.clientId);

    return true;
  }

  @Post(':conversationId/messages/:messageId/reactions')
  @ApiOperation({
    summary: 'React message in conversation',
  })
  @ApiCreatedResponse({
    schema: {
      allOf: [
        { $ref: getSchemaPath(ResponseOkDto) },
        {
          properties: {
            data: {
              $ref: getSchemaPath(MessageReactionDto),
            },
          },
        },
      ],
    },
  })
  @UseGuards(
    CheckAvailableConversationGuard,
    CheckConversationMemberGuard,
    CheckReactableConversationGuard,
    CheckAvailableMessageGuard,
  )
  @UseInterceptors(new ResponseInterceptor(MessageReactionDto))
  async react(
    @Param('conversationId') conversationId: string,
    @Param('messageId') messageId: string,
    @Req() request: RequestWithUser,
    @Body() input: ReactDto,
  ): Promise<MessageReactionDto> {
    const userId = request.user.id;
    input.messageId = messageId;
    input.senderId = userId;
    const message: Message =
      await this.messageRepository.findOneById(messageId);
    if (conversationId !== message.conversationId) {
      throw new ConflictException({
        code: 'MESSAGE_NOT_BELONGS_TO_CONVERSATION',
        message: 'Message is not belongs to conversation',
      });
    }

    const messageReactionDto = await this.messageReactionService.react(input);

    this.eventEmitter.emit(
      MESSAGE_REACTION_EVENT_TYPE.CREATED,
      new MessageReactionCreatedEvent(
        messageReactionDto,
        await this.messageService.processMessageHistory(message, {
          userId,
        }),
        input?.clientId,
      ),
    );

    return messageReactionDto;
  }

  @Delete(':conversationId/messages/:messageId/unreact')
  @ApiOperation({
    summary: 'Unreact message by type in conversation',
  })
  @ApiOkResponse({
    type: ResponseOkDto,
  })
  @UseGuards(
    CheckAvailableConversationGuard,
    CheckConversationMemberGuard,
    CheckReactableConversationGuard,
    CheckAvailableMessageGuard,
  )
  @UseInterceptors(new ResponseInterceptor(ResponseOkDto))
  async unreactByType(
    @Param('conversationId') conversationId: string,
    @Param('messageId') messageId: string,
    @Req() request: RequestWithUser,
    @Body() input: UnreactDto,
  ): Promise<boolean> {
    const userId: string = request.user.id;

    const message: Message =
      await this.messageRepository.findOneById(messageId);
    if (conversationId !== message.conversationId) {
      throw new ConflictException({
        code: 'MESSAGE_NOT_BELONGS_TO_CONVERSATION',
        message: 'Message is not belongs to conversation',
      });
    }

    await this.messageReactionService.unreact({
      messageId,
      senderId: userId,
      type: input.type,
    });

    this.eventEmitter.emit(
      MESSAGE_REACTION_EVENT_TYPE.DELETED,
      new MessageReactionDeletedEvent(
        {
          messageId,
          senderId: userId,
          type: input.type,
        } as MessageReactionDto,
        await this.messageService.processMessageHistory(message, {
          userId,
        }),
        input?.clientId,
      ),
    );

    return true;
  }

  @Get(':conversationId/pinned-messages')
  @ApiOperation({
    summary: 'List pinned messages in conversation',
  })
  @ApiResponsePagination(PinnedMessageDto)
  @UseGuards(CheckAvailableConversationGuard, CheckConversationMemberGuard)
  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.CONVERSATION))
  @UseInterceptors(new ResponseInterceptor(PinnedMessageDto))
  async listPinnedMessages(
    @Param('conversationId') conversationId: string,
    @Query('skip', ParseIntPipe) skip: number,
    @Query('limit', ParseIntPipe) limit: number,
  ) {
    const [items, count] = await this.pinnedMessageRepository.findAndCount({
      select: {
        id: true,
        messageId: true,
        createdAt: true,
        updatedAt: true,
        message: {
          id: true,
          conversationId: true,
          content: true,
          type: true,
          senderId: true,
          createdAt: true,
          updatedAt: true,
        },
      },
      where: {
        message: {
          conversationId,
        },
      },
      relations: {
        message: true,
      },
      order: {
        createdAt: 'DESC',
      },
      skip,
      take: limit,
    });

    if (count < 1) {
      return {
        items: [],
        count: 0,
      };
    }

    const messages = items.map((item) => {
      return item.message;
    });

    const messagesDto =
      await this.messageService.processMessagesHistory(messages);

    const pinnedMessageDtoList: PinnedMessageDto[] = plainToInstance(
      PinnedMessageDto,
      items,
    );

    for (const pinnedMessageDto of pinnedMessageDtoList) {
      pinnedMessageDto.message = messagesDto.find(
        (messageDto: MessageDto) =>
          messageDto.id === pinnedMessageDto.messageId,
      );
    }

    return {
      items: pinnedMessageDtoList,
      count,
    };
  }

  @Post(':conversationId/messages/:messageId/pin')
  @ApiOperation({
    summary: 'Pin message in conversation',
  })
  @ApiCreatedResponse({
    schema: {
      allOf: [
        { $ref: getSchemaPath(ResponseOkDto) },
        {
          properties: {
            data: {
              $ref: getSchemaPath(PinnedMessageDto),
            },
          },
        },
      ],
    },
  })
  @UseGuards(CheckOwnerConversationGuard, CheckAvailableMessageGuard)
  @UseInterceptors(new ResponseInterceptor(PinnedMessageDto))
  async pin(
    @Param('conversationId') conversationId: string,
    @Param('messageId') messageId: string,
    @Req() request: RequestWithUser,
  ): Promise<PinnedMessageDto> {
    const userId: string = request.user.id;

    const pinedMessage = await this.messageService.pin(
      conversationId,
      messageId,
      userId,
    );

    if (pinedMessage) {
      const message = await this.messageRepository.findOne({
        select: {
          id: true,
          conversationId: true,
          parentId: true,
        },
        where: {
          id: messageId,
        },
      });

      this.eventEmitter.emit(
        MESSAGE_EVENT_TYPE.UPDATED,
        new MessageUpdatedEvent({
          ...(message as any),
          isPinned: true,
        } as MessageDto),
      );

      this.eventEmitter.emit(
        MESSAGE_EVENT_TYPE.CREATED,
        new MessageCreatedEvent(pinedMessage.message),
      );
    }

    return pinedMessage;
  }

  @Delete(':conversationId/messages/:messageId/unpin')
  @ApiOperation({
    summary: 'Unpin message in conversation',
  })
  @ApiOkResponse({
    schema: {
      allOf: [
        { $ref: getSchemaPath(ResponseOkDto) },
        {
          properties: {
            data: {
              $ref: getSchemaPath(PinnedMessageDto),
            },
          },
        },
      ],
    },
  })
  @UseGuards(
    CheckOwnerConversationGuard,
    CheckConversationPublicGuard,
    CheckAvailableMessageGuard,
  )
  @UseInterceptors(new ResponseInterceptor(ResponseOkDto))
  async unpin(
    @Param('conversationId') conversationId: string,
    @Param('messageId') messageId: string,
    @Req() request: RequestWithUser,
    @Body() input: UnPinMessageDto,
  ): Promise<boolean> {
    await this.pinnedMessageRepository.softDelete({
      messageId,
    });

    const message = await this.messageRepository.findOne({
      select: {
        id: true,
        conversationId: true,
        parentId: true,
      },
      where: {
        id: messageId,
      },
    });
    this.eventEmitter.emit(
      MESSAGE_EVENT_TYPE.UPDATED,
      new MessageUpdatedEvent(
        {
          ...message,
          isPinned: false,
        } as any,
        input?.clientId,
      ),
    );

    return true;
  }

  @Patch(':conversationId/read')
  @ApiOperation({
    summary: 'Update read time of conversation',
  })
  @ApiOkResponse({
    schema: {
      allOf: [
        { $ref: getSchemaPath(ResponseOkDto) },
        {
          properties: {
            data: {
              type: 'array',
              items: { $ref: getSchemaPath(RetrieveReadTimeDto) },
            },
          },
        },
      ],
    },
  })
  // @UseGuards(CheckConversationMemberGuard)
  @UseInterceptors(new ResponseInterceptor(RetrieveReadTimeDto))
  async updateReadLastTime(
    @Param('conversationId') conversationId: string,
    @Req() request: RequestWithUser,
    @Body() input: UpdateReadTimeDto,
  ): Promise<RetrieveReadTimeDto> {
    const readAt = moment().toDate();

    await this.conversationMemberService.updateReadLastMessageTime(
      {
        conversationId,
        userId: request.user.id,
        ...(!isEmpty(input?.threadId) && { messageId: input.threadId }),
        ...(!isEmpty(input?.messageId) && { messageId: input.messageId }),
      },
      readAt,
    );

    return {
      readAt: readAt.toISOString(),
    } as RetrieveReadTimeDto;
  }

  @Delete(':conversationId/message-attachments/:messageAttachmentId')
  @ApiOperation({
    summary: 'Delete message in conversation',
  })
  @ApiOkResponse({
    type: ResponseOkDto,
  })
  @UseGuards(
    CheckAvailableConversationGuard,
    CheckConversationMemberGuard,
    CheckConversationPublicGuard,
  )
  @UseInterceptors(new ResponseInterceptor(ResponseOkDto))
  async deleteMessageAttachment(
    @Param('conversationId') conversationId: string,
    @Param('messageAttachmentId') messageAttachmentId: string,
    @Req() request: RequestWithUser,
    @Body() input: DeleteAttachmentDto,
  ): Promise<boolean> {
    const userId: string = request.user.id;

    const conversationMember =
      await this.conversationMemberRepository.findOneBy({
        conversationId,
        userId,
      });

    const isOwner = conversationMember.isOwner === SERVER_MEMBER_TYPE.OWNER;

    const messageAttachment: MessageAttachment =
      await this.messageAttachmentRepository.findOne({
        where: {
          id: messageAttachmentId,
        },
        select: {
          id: true,
          messageId: true,
        },
      });
    if (!messageAttachment) {
      throw new ConflictException({
        code: 'MESSAGE_ATTACHMENT_NOT_FOUND',
        message: 'Message attachment is not found',
      });
    }

    const message: Message = await this.messageRepository.findOneById(
      messageAttachment.messageId,
    );
    if (conversationId !== message?.conversationId) {
      throw new ConflictException({
        code: 'MESSAGE_NOT_BELONGS_TO_CONVERSATION',
        message: 'Message is not belongs to this conversation',
      });
    }
    if (!isOwner && userId !== message?.senderId) {
      throw new ForbiddenException({
        code: 'CANNOT_DELETE_MESSAGE_OF_OTHER_USER',
        message: 'Cannot delete message of other user',
      });
    }

    if (
      !isOwner &&
      conversationMember.bannedAt &&
      message.status === MESSAGE_STATUS.PUBLISHED
    ) {
      throw new ForbiddenException({
        code: 'CANNOT_DELETE_MESSAGE_ATTACHMENT',
        message: 'Cannot delete message attachment',
      });
    }

    const updatedTime: Date = moment().toDate();
    await this.messageAttachmentRepository.softDelete(messageAttachment.id);
    await this.messageRepository.update(message.id, {
      updatedAt: updatedTime,
    });
    message.updatedAt = updatedTime;

    this.eventEmitter.emit(
      MESSAGE_EVENT_TYPE.UPDATED,
      new MessageUpdatedEvent(
        (await this.messageService.processMessageHistory(
          message,
        )) as MessageDto,
        input?.clientId,
      ),
    );

    return true;
  }

  @Get(':conversationId/message-attachments/:messageAttachmentId/download')
  @ApiOperation({
    summary: 'Download attachment in message',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Download file in message',
    content: {
      'image/jpeg': {},
      'image/png': {},
      'image/gif': {},
      'image/pjpeg': {},
      'video/mp4': {},
      'video/hevc': {},
      'video/mkv': {},
      'video/mov': {},
      'video/quicktime': {},
      'video/x-matroska': {},
    },
  })
  @UseGuards(CheckAvailableConversationGuard, CheckConversationMemberGuard)
  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.CONVERSATION))
  async download(
    @Param('conversationId') conversationId: string,
    @Param('messageAttachmentId') messageAttachmentId: string,
    @Req() request: RequestWithUser,
    @Res() response: Response,
  ) {
    const messageAttachment: MessageAttachment =
      await this.messageAttachmentRepository.findOne({
        where: {
          id: messageAttachmentId,
          message: {
            conversationId,
          },
        },
        select: {
          id: true,
          url: true,
          originalFileName: true,
          messageId: true,
        },
      });
    if (!messageAttachment) {
      throw new ConflictException({
        code: 'NOT_FOUND_MESSAGE_ATTACHMENT',
        message: 'Not found message attachment',
      });
    }

    const { fileBuffer, fileType } = await this.s3Service.processFile(
      messageAttachment.url,
    );

    let filename = messageAttachment.id;
    if (messageAttachment?.originalFileName) {
      const availableRegex = /^[a-zA-Z0-9\-_.' ]+$/;
      const matchedName =
        availableRegex.test(messageAttachment?.originalFileName ?? '') ?? false;

      filename = matchedName
        ? messageAttachment?.originalFileName
        : messageAttachment.id;
    }

    response.setHeader('Content-Type', fileType.mime);
    response.setHeader(
      'Content-Disposition',
      `attachment; filename=${filename}`,
    );
    response.send(fileBuffer);
  }

  @Get('/url-meta')
  @ApiQuery({
    name: 'url',
    required: true,
  })
  @ApiOperation({
    summary: 'Get detail url metadata',
  })
  @ApiOkResponse({
    schema: {
      allOf: [
        { $ref: getSchemaPath(ResponseOkDto) },
        {
          properties: {
            data: {
              $ref: getSchemaPath(UrlMetaDto),
            },
          },
        },
      ],
    },
  })
  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.CONVERSATION))
  @UseInterceptors(new ResponseInterceptor(UrlMetaDto))
  async getUrlMetadata(
    @Query('url') url: string,
  ): Promise<UrlMetaDto | object> {
    const inputUrl = extractUrls(url);
    if (inputUrl.length) {
      return await this.ogpService.fetchMetadataUrl(inputUrl[0], false);
    }

    return { url, metadata: {} };
  }

  @Post(':conversationId/generate-presigned-url')
  @HttpCode(HttpStatus.OK)
  @UseGuards(CheckAvailableConversationGuard, CheckConversationMemberGuard)
  @UseInterceptors(new ResponseInterceptor(PresignedUrlDto))
  @ApiOperation({
    summary: 'Generate presigned URL',
  })
  @ApiOkResponse({
    schema: {
      allOf: [
        { $ref: getSchemaPath(ResponseOkDto) },
        {
          properties: {
            data: {
              $ref: getSchemaPath(PresignedUrlDto),
            },
          },
        },
      ],
    },
  })
  async generatePresignedUrlConversation(
    @Param('conversationId') conversationId: string,
    @Body() input: GeneratePresignedUrlDto,
  ): Promise<PresignedUrlDto> {
    const fileNameArr: string[] = input.fileName?.split('.');
    const fileExtension: string =
      fileNameArr.length > 0 ? fileNameArr[fileNameArr.length - 1] : '';

    return {
      presignedUrl: await this.s3Service.getPresignedUrl(
        `conversations/${conversationId}/${moment().format('YYYYMMDD')}/${v4()}${fileExtension ? '.' + fileExtension : ''}`,
      ),
    };
  }

  @Delete(':conversationId/messages/:messageId/embed/:embedId')
  @ApiOperation({
    summary: 'Delete message embed in conversation',
  })
  @ApiCreatedResponse({
    type: ResponseOkDto,
  })
  @UseGuards(CheckUpdateMessageEmbedGuard)
  @UseInterceptors(new ResponseInterceptor(ResponseOkDto))
  async deleteMessageEmbed(
    @Param('conversationId') conversationId: string,
    @Param('messageId') messageId: string,
    @Param('embedId') embedId: string,
  ): Promise<boolean> {
    await this.messageEmbedRepository.softDelete(embedId);
    this.eventEmitter.emit(
      MESSAGE_EMBED_EVENT_TYPE.DELETED,
      new MessageEmbedDeletedEvent('', embedId, messageId, conversationId),
    );
    return true;
  }

  @Get('/unread-notification')
  @ApiOperation({
    summary: 'Get total unread notification',
  })
  @ApiOkResponse({
    schema: {
      allOf: [
        { $ref: getSchemaPath(ResponseOkDto) },
        {
          properties: {
            data: {
              type: 'number',
            },
          },
        },
      ],
    },
  })
  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.CONVERSATION))
  @UseInterceptors(new ResponseInterceptor(UrlMetaDto))
  async getTotalUnreadNotification(
    @Req() request: RequestWithUser,
  ): Promise<number> {
    return this.messageNotificationService.getTotalUnreadByUser(
      request.user.id,
    );
  }

  @Get('/unread-notification/count')
  @ApiOperation({
    summary: 'Count unread notification by conversation',
  })
  @ApiOkResponse({
    schema: {
      allOf: [
        { $ref: getSchemaPath(ResponseOkDto) },
        {
          properties: {
            data: {
              $ref: getSchemaPath(UnreadNotificationDto),
            },
          },
        },
      ],
    },
  })
  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.CONVERSATION))
  @UseInterceptors(new ResponseInterceptor(UnreadNotificationDto))
  async countUnreadNotificationByConversation(
    @Req() request: RequestWithUser,
    @Query() query: UnreadNotificationQueryDto,
  ): Promise<UnreadNotificationDto[]> {
    const { id, parentId } = query;
    const unreadNotifications =
      await this.messageNotificationService.getUnreadTotalByConversation({
        userId: request.user.id,
        conversationId: id,
        parentConversationId: parentId,
      });
    return Object.keys(unreadNotifications).map((conversationId: string) => {
      return {
        conversationId,
        notifications: unreadNotifications?.[conversationId],
      } as UnreadNotificationDto;
    });
  }

  @Get('/unread-message/count')
  @ApiOperation({
    summary: 'Count unread message by conversation',
  })
  @ApiOkResponse({
    schema: {
      allOf: [
        { $ref: getSchemaPath(ResponseOkDto) },
        {
          properties: {
            data: {
              $ref: getSchemaPath(UnreadMessageDto),
            },
          },
        },
      ],
    },
  })
  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.CONVERSATION))
  @UseInterceptors(new ResponseInterceptor(UnreadMessageDto))
  async countUnreadMessageByConversation(
    @Req() request: RequestWithUser,
    @Query() query: UnreadNotificationQueryDto,
  ): Promise<UnreadMessageDto[]> {
    const { id, parentId } = query;
    const unreadMessages =
      await this.conversationMemberService.getMessageUnreadByConversation({
        userId: request.user.id,
        conversationId: id,
        parentConversationId: parentId,
      });
    return Object.keys(unreadMessages).map((conversationId: string) => {
      return {
        conversationId,
        ...unreadMessages?.[conversationId],
      } as UnreadMessageDto;
    });
  }
}
