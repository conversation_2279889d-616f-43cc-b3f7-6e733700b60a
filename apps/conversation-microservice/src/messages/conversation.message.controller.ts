import { ConversationService } from '@app/conversation/services/conversation.service';
import {
  Controller,
  Inject,
  UseFilters,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ConversationMemberRepositoryInterface,
  ConversationRepositoryInterface,
  MessageMentionRepositoryInterface,
  MessageNotificationRepositoryInterface,
  MessageReactionRepositoryInterface,
  MessageRepositoryInterface,
} from '@app/shared/database/repositories';
import { KafkaGuard } from '@app/shared/guards/kafka.guard';
import {
  LoggingInterceptor,
  ReadOnlyInterceptor,
} from '@app/shared/interceptors';
import { AppLogger } from '@app/shared/logger/app.logger';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ClsGuard } from 'nestjs-cls';
import {
  CONVERSATION_MICROSERVICE_MESSAGE_PATTERN,
  INSERT_CHUNK_SIZE,
} from '@app/shared/constants';
import { MessageService } from '@app/conversation/services/message.service';
import { ConversationMemberService } from '@app/conversation/services/conversation-member.service';
import { MessageNotificationService } from '@app/conversation/services/message-notification.service';
import { chunk, isArray, isEmpty, isUndefined, uniq, uniqBy } from 'lodash';
import { And, FindOptionsWhere, In, IsNull, LessThan, Not, Or } from 'typeorm';
import {
  Conversation,
  ConversationMember,
  Message,
  MessageNotification,
} from '@app/shared/database/entities';
import { UpdateMessageNotificationDto } from '@app/conversation/dto/update-message-notification.dto';
import { MessageNotificationDto } from '@app/conversation/dto/message-notification.dto';
import { MessageActionService } from '@app/conversation/services/message-action.service';
import { GetMessageActionDto } from '@app/conversation/dto/get-message-action.dto';
import {
  MESSAGE_STATUS,
  MENTION_TYPE,
  MESSAGE_NOTIFICATION_TYPE,
  CONVERSATION_MEMBER_NOTIFICATION_TYPE,
} from '@app/conversation/constants';
import moment from 'moment';
import { ChimeService } from '@app/aws';
import { MessageEmbedService } from '@app/conversation/services/message-embed.service';
import { SERVER_DEFAULT_NOTIFICATION } from '@app/community/server/constants';
import { KafkaExceptionFilter } from '@app/shared/exceptions';
import { DB_SERVICE_NAME } from '@app/shared/database/constants';

@Controller()
@UseGuards(ClsGuard, KafkaGuard)
@UseFilters(KafkaExceptionFilter)
@UseInterceptors(LoggingInterceptor)
export class ConversationMessageController {
  constructor(
    private readonly logger: AppLogger,
    @Inject('ConversationRepositoryInterface')
    private readonly conversationRepository: ConversationRepositoryInterface,
    @Inject('ConversationMemberRepositoryInterface')
    private readonly conversationMemberRepository: ConversationMemberRepositoryInterface,
    @Inject('MessageRepositoryInterface')
    private readonly messageRepository: MessageRepositoryInterface,
    @Inject('MessageReactionRepositoryInterface')
    private readonly messageReactionRepository: MessageReactionRepositoryInterface,
    @Inject('MessageMentionRepositoryInterface')
    private readonly messageMentionRepository: MessageMentionRepositoryInterface,
    @Inject('MessageNotificationRepositoryInterface')
    private readonly messageNotificationRepository: MessageNotificationRepositoryInterface,
    private readonly chimeService: ChimeService,
    private readonly messageService: MessageService,
    private readonly conversationMemberService: ConversationMemberService,
    private readonly messageNotificationService: MessageNotificationService,
    private readonly messageActionService: MessageActionService,
    private readonly messageEmbedService: MessageEmbedService,
    private readonly conversationService: ConversationService,
  ) {}

  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.CONVERSATION))
  @MessagePattern(CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.CONVERSATION_GET)
  async handleFindOneConversation(
    @Payload()
    payload: {
      id: string;
    },
  ) {
    this.logger.debug(
      `find one conversation with payload: ${JSON.stringify(payload)}`,
    );
    const { id } = payload;
    const conversation = await this.conversationRepository.findOneById(id);

    if (!conversation) {
      return null;
    }

    this.logger.debug(`find one conversation success`);

    return { ...conversation };
  }

  @MessagePattern(CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.CONVERSATION_CREATE)
  async handleConversationCreate(
    @Payload()
    payload: {
      conversation: Conversation;
    },
  ) {
    this.logger.debug(
      `create conversation with payload: ${JSON.stringify(payload)}`,
    );
    const { conversationMembers } = payload.conversation;

    const conversation = await this.conversationRepository.save({
      id: payload.conversation.id,
      name: payload.conversation.name,
      code: payload.conversation.code,
      parentId: payload.conversation.parentId ?? null,
      isAllowedChat: !isUndefined(payload.conversation?.isAllowedChat)
        ? payload.conversation?.isAllowedChat
        : true,
      isAllowedReaction: !isUndefined(payload.conversation?.isAllowedReaction)
        ? payload.conversation?.isAllowedReaction
        : true,
      status: payload.conversation?.status ?? false,
    } as Conversation);

    if (!conversation) {
      return false;
    }

    const newConversationMembers = [];
    uniqBy(conversationMembers, 'userId').map((member) => {
      newConversationMembers.push({
        conversationId: conversation.id,
        userId: member.userId,
        memberId: member.memberId,
        isOwner: member.isOwner,
        bannedAt: member.bannedAt,
        setting: {
          mute: null,
        },
        lastReadTime: moment().toDate(),
      } as ConversationMember);
    });

    // Save new conversation members
    await this.conversationMemberRepository.save(newConversationMembers);

    this.logger.debug(`create conversation success`);
    return true;
  }

  @MessagePattern(CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.CONVERSATION_UPDATE)
  async handleConversationUpdate(
    @Payload()
    payload: {
      id: string | string[];
      dataUpdate: Conversation;
    },
  ) {
    this.logger.debug(
      `update conversation with payload: ${JSON.stringify(payload)}`,
    );
    const { id, dataUpdate } = payload;

    if (!id || !dataUpdate) {
      return false;
    }

    await this.conversationRepository.updateBy(
      {
        id: isArray(id) ? In(uniq(id)) : id,
      },
      dataUpdate,
    );
    if (!dataUpdate.isAllowedChat) {
      await this.messageService.cancelMessageSchedule(id);
    }
    this.logger.debug(`update conversation success`);
    return true;
  }

  @MessagePattern(CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.CONVERSATION_DELETE)
  async handleConversationDelete(
    @Payload()
    payload: {
      id: string | string[];
    },
  ) {
    this.logger.debug(
      `delete conversation with payload: ${JSON.stringify(payload)}`,
    );
    const { id } = payload;
    if (!id) {
      return false;
    }
    const conversations = await this.conversationRepository.find({
      select: {
        id: true,
        code: true,
      },
      where: {
        id: In(uniq(isArray(id) ? id : [id])),
      },
    });
    const conversationIds = conversations.map((c) => c.id);
    if (conversationIds.length === 0) {
      return false;
    }

    // Delete message
    await this.messageRepository.softDelete({
      conversationId: In(conversationIds),
    });

    // Delete message reaction
    await this.messageReactionRepository
      .createQueryBuilder()
      .softDelete()
      .where(
        'message_id IN (SELECT m.id FROM messages m WHERE m.conversation_id IN (:...conversationIds))',
        { conversationIds },
      )
      .execute();

    // Delete message mentions
    await this.messageMentionRepository.softDelete({
      conversationId: In(conversationIds),
    });

    // Delete message schedule
    await this.messageService.cancelMessageSchedule(conversationIds);

    // Publish event
    for (const conversation of conversations) {
      await this.chimeService.deleteMeeting(conversation.code);
    }

    this.logger.debug(`delete conversation success ${JSON.stringify(payload)}`);
    return true;
  }

  @MessagePattern(CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.MEMBER_CREATE)
  async handleConversationMemberCreate(
    @Payload()
    payload: {
      members: ConversationMember[];
    },
  ) {
    const { members } = payload;
    // Check member exist
    if (!members || members.length === 0) {
      return;
    }
    const uniqMembers = uniqBy(
      members,
      (item) => `${item.conversationId}-${item.userId}`,
    );

    for (const chunkMembers of chunk(uniqMembers, INSERT_CHUNK_SIZE)) {
      if (chunkMembers.length === 0) {
        continue;
      }
      // Check member exist in conversation
      const existMembers = await this.conversationMemberRepository.find({
        select: {
          id: true,
          conversationId: true,
          userId: true,
        },
        where: {
          userId: In(uniq(chunkMembers.map((m) => m.userId))),
          conversationId: In(uniq(chunkMembers.map((m) => m.conversationId))),
        },
      });

      const newMembers = chunkMembers
        .filter(
          (m) =>
            !existMembers.some(
              (em) =>
                em.userId === m.userId &&
                em.conversationId === m.conversationId,
            ),
        )
        .map((member) => {
          return this.conversationMemberRepository.create({
            ...member,
            lastReadTime: moment().toDate(),
            setting: {
              mute: null,
            },
          });
        });
      if (newMembers.length === 0) {
        continue;
      }
      // Create new member
      await this.conversationMemberRepository.insert(newMembers);

      // Update message notification of members
      await this.messageNotificationRepository.updateBy(
        {
          userId: In(newMembers.map((m) => m.userId)),
          conversationId: In(newMembers.map((m) => m.conversationId)),
          isRead: false,
        },
        {
          isRead: true,
          readAt: new Date(),
        },
      );
    }

    this.logger.debug(
      `create member success with payload: ${JSON.stringify(payload)}`,
    );

    return true;
  }

  @MessagePattern(CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.MEMBER_UPDATE)
  async handleConversationMemberUpdate(
    @Payload()
    payload: {
      where: {
        id: string | string[];
        userIds: string[];
        conversationIds?: string[];
        memberIds?: string[];
      };
      updateData: ConversationMember;
    },
  ) {
    const { where, updateData } = payload;
    // Check member exist
    if (isEmpty(where) || isEmpty(updateData)) {
      return;
    }

    await this.conversationMemberRepository.updateBy(
      {
        ...(where.id && { id: isArray(where.id) ? In(where.id) : where.id }),
        ...(where.userIds && { userId: In(uniq(where.userIds)) }),
        ...(where.conversationIds && {
          conversationId: In(uniq(where.conversationIds)),
        }),
        ...(where.memberIds && { memberId: In(uniq(where.memberIds)) }),
        ...(updateData?.notificationType ===
        CONVERSATION_MEMBER_NOTIFICATION_TYPE.ALL
          ? {
              notificationDisabledUntil: Or(IsNull(), LessThan(new Date())),
              // notificationType: CONVERSATION_MEMBER_NOTIFICATION_TYPE.DISABLE,
            }
          : {}),
      },
      updateData,
    );

    this.logger.debug(
      `update member success with payload: ${JSON.stringify(payload)}`,
    );

    return true;
  }

  @MessagePattern(CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.MEMBER_DELETE)
  async handleConversationMemberDelete(
    @Payload()
    payload: {
      userIds?: string[];
      conversationIds?: string[];
      memberIds?: string[];
    },
  ) {
    const { userIds, conversationIds, memberIds } = payload;
    if (
      (!userIds || userIds?.length === 0) &&
      (!conversationIds || conversationIds?.length === 0) &&
      (!memberIds || memberIds?.length === 0)
    ) {
      return false;
    }
    await this.conversationMemberRepository.softDelete({
      ...(userIds?.length && { userId: In(userIds) }),
      ...(conversationIds?.length && { conversationId: In(conversationIds) }),
      ...(memberIds?.length && { memberId: In(memberIds) }),
    });

    // Delete message schedule
    await this.messageService.cancelMessageSchedule(conversationIds, userIds);

    this.logger.debug(
      `delete member success with payload: ${JSON.stringify(payload)}`,
    );
    return true;
  }

  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.CONVERSATION))
  @MessagePattern(CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.MESSAGE_GETS)
  async handleGetListMessageByCondition(
    @Payload()
    payload: {
      senderId?: string;
      mentionId?: string;
      conversationId: string[];
      messageIds?: string[];
      userId?: string;
      isOwner?: number;
      type?: number;
      isParent?: boolean;
      skip?: number;
      limit?: number;
      orderByCreatedAt?: 'ASC' | 'DESC';
    },
  ) {
    return await this.messageService.searchMessages(payload);
  }

  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.CONVERSATION))
  @MessagePattern(
    CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.MESSAGE_UNREAD_TOTAL,
  )
  async handleGetMessageUnreadTotal(
    @Payload() payload: { conversationIds: string[]; userId: string },
  ) {
    return await this.conversationMemberService.getMessageUnreadByConversation({
      conversationId: payload.conversationIds,
      userId: payload.userId,
    });
  }

  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.CONVERSATION))
  @MessagePattern(CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.NOTIFICATION_GETS)
  async handleGetNotifications(@Payload() payload: any) {
    return await this.messageNotificationService.getListByCondition(payload);
  }

  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.CONVERSATION))
  @MessagePattern(
    CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.NOTIFICATION_UNREAD_TOTAL,
  )
  async handleGetNotificationUnreadTotal(
    @Payload() payload: { conversationIds: string[]; userId: string },
  ) {
    return await this.messageNotificationService.getUnreadTotalByConversation({
      conversationId: payload.conversationIds,
      userId: payload.userId,
    });
  }

  @MessagePattern(CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.NOTIFICATION_UPDATE)
  async handleUpdateMessageNotification(
    @Payload()
    payload: {
      condition: MessageNotificationDto;
      updateData?: UpdateMessageNotificationDto;
    },
  ) {
    const { updateData, condition } = payload;
    const { userId, conversationId, isRead, type, sendStatus } = condition;

    if (isUndefined(sendStatus)) {
      await this.messageNotificationService.updateByCondition(
        {
          ...(!isEmpty(userId)
            ? {
                userId: isArray(userId) ? In(userId) : userId,
              }
            : {}),
          ...(!isEmpty(conversationId)
            ? {
                conversationId: isArray(conversationId)
                  ? In(conversationId)
                  : conversationId,
              }
            : {}),
          ...(!isEmpty(isRead) ? { isRead } : {}),
          ...(type === SERVER_DEFAULT_NOTIFICATION.ONLY_MENTION
            ? {
                type: MESSAGE_NOTIFICATION_TYPE.MENTION,
                isRead: false,
                messageMention: {
                  type: MENTION_TYPE.USER,
                  ...(!isEmpty(userId)
                    ? {
                        targetId: isArray(userId) ? In(userId) : userId,
                      }
                    : {}),
                },
              }
            : { isRead: false }),
        },
        updateData as MessageNotification,
      );
    } else {
      // Update number remain notify when send failed
      if (!sendStatus) {
        this.messageNotificationRepository.query(
          `UPDATE message_notifications SET remain_notify = remain_notify - 1 WHERE user_id = $1 AND remain_notify > 0 AND notify_time IS NULL`,
          [userId],
        );
      }
    }

    return true;
  }

  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.CONVERSATION))
  @MessagePattern(
    CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.MESSAGE_FAVORITE_GETS,
  )
  async handleGetFavoriteMessage(
    @Payload()
    payload: GetMessageActionDto,
  ) {
    return await this.messageActionService.getFavoriteMessages(payload);
  }

  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.CONVERSATION))
  @MessagePattern(
    CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.MESSAGE_SCHEDULE_GETS,
  )
  async getListMessageSchedule(condition: any) {
    const { limit, skip, conversationIds, userId } = condition;
    const query: FindOptionsWhere<Message> = {
      status: MESSAGE_STATUS.SCHEDULED,
      senderId: userId,
      conversationId: In(conversationIds),
    };
    let messages = [];
    let total = 0;

    const [items, count] = await this.messageRepository.findAndCount({
      select: {
        id: true,
        conversationId: true,
        senderId: true,
        parentId: true,
        type: true,
        content: true,
        replyThread: true,
        createdAt: true,
        updatedAt: true,
        scheduleAt: true,
        status: true,
      },
      where: query,
      take: limit,
      skip: skip ?? 0,
      order: {
        createdAt: 'DESC',
      },
    });
    messages = items;
    total = count;

    return {
      items: await this.messageService.processMessagesHistory(messages, {
        userId,
      }),
      count: total,
    };
  }

  @MessagePattern(
    CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.MESSAGE_FORCE_DELETE,
  )
  handleForceDeleteMessage(
    @Payload()
    payload: {
      userIds?: string[];
    },
  ) {
    if (!isEmpty(payload?.userIds)) {
      this.conversationMemberRepository.deleteBy({
        userId: In(payload.userIds),
      });
    }
    this.messageService.handleForceDeleteMessage(payload?.userIds);
    return true;
  }

  @MessagePattern(
    CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.SCHEDULE_MESSAGE_EMBED_METADATA_UPDATE,
  )
  handleUpdateEmbedMetadataUrl() {
    this.messageEmbedService.updateEmbedMetadataUrl();
    return true;
  }

  @MessagePattern(
    CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.SCHEDULE_SEND_MESSAGE_SCHEDULE,
  )
  handleSendMessageSchedule() {
    this.messageService.sendMessageSchedule();
    return true;
  }

  @UseInterceptors(ReadOnlyInterceptor(DB_SERVICE_NAME.CONVERSATION))
  @MessagePattern(
    CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.MESSAGE_SCHEDULE_COUNT,
  )
  async countMessageSchedule(condition: {
    conversationId: string[];
    userId: string;
  }) {
    const { conversationId, userId } = condition;

    const messageScheduleCounts = await this.messageRepository
      .createQueryBuilder('message')
      .select('message.conversationId', 'conversationId')
      .addSelect('COUNT(*)', 'count')
      .where('message.conversationId IN (:...conversationId)', {
        conversationId,
      })
      .andWhere('message.status = :status', {
        status: MESSAGE_STATUS.SCHEDULED,
      })
      .andWhere('message.senderId = :userId', { userId })
      .groupBy('message.conversationId')
      .getRawMany();

    return messageScheduleCounts;
  }

  @MessagePattern(CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.MEMBER_GETS)
  async getConversationMember(
    @Payload()
    payload: {
      userId?: string | string[];
      conversationId?: string | string[];
      notificationType?: number;
      muteExpired?: boolean;
      skip?: number;
      limit: number;
    },
  ) {
    const conversationMembers = await this.conversationMemberRepository.find({
      where: {
        ...(payload.userId
          ? {
              userId: isArray(payload.userId)
                ? In(payload.userId)
                : payload.userId,
            }
          : {}),
        ...(payload.conversationId
          ? {
              conversationId: isArray(payload.conversationId)
                ? In(payload.conversationId)
                : payload.conversationId,
            }
          : {}),
        ...(!isEmpty(payload.notificationType) && {
          notificationType: payload.notificationType,
        }),
        ...(payload.muteExpired === true && {
          notificationDisabledUntil: And(Not(IsNull()), LessThan(new Date())),
        }),
      },
      ...(!isUndefined(payload.skip) ? { skip: payload.skip } : {}),
      ...(!isUndefined(payload.limit) ? { take: payload.limit } : {}),
    });

    if (conversationMembers.length < 1) {
      return [];
    }

    return conversationMembers;
  }

  @MessagePattern(
    CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.NOTIFICATION_UNREAD_BY_MEMBER_COUNT,
  )
  async countUnreadNotification(
    @Payload()
    payload: {
      userIds?: string[];
      skip: number;
      limit: number;
    },
  ) {
    return await this.messageNotificationService.countMemberUnreadNotification(
      payload,
    );
  }

  @MessagePattern(CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.MESSAGE_CREATE)
  async createMessagePost(
    @Payload()
    payload: Message,
  ) {
    return await this.messageRepository.save(payload);
  }

  @MessagePattern(
    CONVERSATION_MICROSERVICE_MESSAGE_PATTERN.CONVERSATION_GET_LIST,
  )
  async getConversationList(conversationIds: string[]) {
    const conversations = await this.conversationService.getList({
      where: { id: In(conversationIds) },
    });
    return conversations;
  }
}
