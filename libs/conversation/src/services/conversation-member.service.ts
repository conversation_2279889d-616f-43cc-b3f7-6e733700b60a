import { ConflictException, Inject, Injectable } from '@nestjs/common';
import {
  Conversation,
  ConversationMember,
  MessageNotification,
} from '@app/shared/database/entities';
import { ConversationMemberRepositoryInterface } from '@app/shared/database/repositories';
import { SettingConversationNotificationDto } from '@app/conversation/dto/setting-conversation-notification.dto';
import moment from 'moment';
import {
  CONVERSATION_MEMBER_EVENT_TYPE,
  CONVERSATION_MEMBER_NOTIFICATION_TYPE,
  CONVERSATION_MEMBER_TYPE,
  MESSAGE_STATUS,
} from '@app/conversation/constants';
import { Brackets, FindOptionsWhere, In, IsNull } from 'typeorm';
import { isArray, isEmpty, isNumber, uniq } from 'lodash';
import { AppLogger } from '@app/shared/logger/app.logger';
import { MessageNotificationService } from './message-notification.service';
import { CommunityKafka } from '@app/community/community.kafka';
import { ConversationMemberUpdatedEvent } from '../events/member.event';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { isNotEmpty } from 'class-validator';

@Injectable()
export class ConversationMemberService {
  constructor(
    private readonly logger: AppLogger,
    @Inject('ConversationMemberRepositoryInterface')
    private readonly conversationMemberRepository: ConversationMemberRepositoryInterface,
    private readonly messageNotificationService: MessageNotificationService,
    private readonly communityKafka: CommunityKafka,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Check user is owner conversation.
   *
   * @param conversationId
   * @param userId
   */
  async checkOwner(conversationId: string, userId: string): Promise<boolean> {
    return await this.conversationMemberRepository.existsBy({
      conversationId,
      userId,
      isOwner: CONVERSATION_MEMBER_TYPE.OWNER,
    });
  }

  async getOneConversationMember(
    conversationId: string,
    userId: string,
  ): Promise<ConversationMember> {
    return await this.conversationMemberRepository
      .createQueryBuilder('conversation_members')
      .select([
        'conversation_members.id',
        'conversations.id',
        'conversation_members.setting',
        'conversation_members.isOwner',
      ])
      .innerJoin('conversation_members.conversation', 'conversations')
      .where('conversations.id = :conversationId', { conversationId })
      .andWhere('conversation_members.userId = :userId', { userId })
      .getOne();
  }

  async getDetail(
    conversationId: string,
    userId: string,
  ): Promise<ConversationMember> {
    const conversationMember: ConversationMember =
      await this.getOneConversationMember(conversationId, userId);
    if (!conversationMember) {
      throw new ConflictException({
        code: 'USER_NOT_CONVERSATION_MEMBER',
        message: 'User is not conversation member',
      });
    }

    if (conversationId !== conversationMember.conversation.id) {
      throw new ConflictException({
        code: 'CONVERSATION_NOT_BELONGS_TO_SERVER',
        message: 'Conversation is not belongs to this server',
      });
    }

    return conversationMember;
  }

  async settingNotification(
    conversationId: string,
    userId: string,
    input: SettingConversationNotificationDto,
  ): Promise<boolean> {
    const conversationMember: ConversationMember = await this.getDetail(
      conversationId,
      userId,
    );

    const isMute: boolean = input.mute;
    let muteEndTime: Date = null;
    let muteStartTime: Date = null;
    if (isMute) {
      const startTime = input.muteStartTime
        ? moment(input.muteStartTime)
        : moment();
      muteStartTime = startTime.toDate();
      muteEndTime = startTime.add(input.muteDuration, 'hours').toDate();

      // Turn off notifications until enabled
      if (isNumber(input.muteDuration) && input.muteDuration === 0) {
        muteEndTime = new Date('2222-01-01'); // Hardcode end time
      }
    }

    const updateData = {
      setting: {
        ...conversationMember.setting,
        mute: isMute
          ? {
              duration: input.muteDuration,
              startTime: muteStartTime,
              endTime: input.muteDuration > 0 ? muteEndTime : null,
            }
          : null,
      },
      notificationType: isMute
        ? CONVERSATION_MEMBER_NOTIFICATION_TYPE.DISABLE
        : CONVERSATION_MEMBER_NOTIFICATION_TYPE.ALL,
      notificationDisabledUntil: muteEndTime,
    } as ConversationMember;

    const serverMember = await this.communityKafka.findOneMemberById(
      conversationMember.memberId,
    );

    if (serverMember?.setting?.notification?.mute) {
      updateData.notificationType =
        CONVERSATION_MEMBER_NOTIFICATION_TYPE.DISABLE;
    }
    const updateResult = await this.conversationMemberRepository.update(
      conversationMember.id,
      updateData,
    );

    if (updateResult) {
      this.eventEmitter.emit(
        CONVERSATION_MEMBER_EVENT_TYPE.UPDATED,
        new ConversationMemberUpdatedEvent([conversationMember], updateData),
      );
    }

    return updateResult.affected > 0;
  }

  /**
   * Update read last message time.
   *
   *
   * @param where
   * @param readAt
   */
  async updateReadLastMessageTime(
    where: {
      conversationId?: string | string[];
      userId?: string | string[];
      messageId?: string | string[];
    },
    readAt: Date = new Date(),
  ): Promise<void> {
    // Update last read time
    const condition = {
      ...(!isEmpty(where.conversationId) && {
        conversationId: In(
          isArray(where.conversationId)
            ? where.conversationId
            : [where.conversationId],
        ),
      }),
      ...(!isEmpty(where.userId) && {
        userId: In(isArray(where.userId) ? where.userId : [where.userId]),
      }),
    };
    await this.conversationMemberRepository.updateBy(
      condition as FindOptionsWhere<ConversationMember>,
      {
        lastReadTime: readAt,
      },
    );

    // Update read time of message notification
    this.messageNotificationService.updateByCondition(
      isEmpty(where.messageId)
        ? {
            isRead: false,
            message: {
              replyThread: false,
            },
            ...condition,
          }
        : [
            {
              isRead: false,
              ...condition,
              messageId: In(
                isArray(where.messageId) ? where.messageId : [where.messageId],
              ),
            },
            {
              isRead: false,
              ...condition,
              message: {
                parentId: In(
                  isArray(where.messageId)
                    ? where.messageId
                    : [where.messageId],
                ),
              },
            },
          ],
      {
        isRead: true,
        readAt,
      } as MessageNotification,
    );
  }

  async getMuteConversationMembers(
    conversationId: string,
    userIds: string[],
  ): Promise<string[]> {
    const conversationMembers = await this.conversationMemberRepository.find({
      select: {
        id: true,
        userId: true,
        conversationId: true,
        notificationType: true,
        notificationDisabledUntil: true,
      },
      where: {
        notificationType: CONVERSATION_MEMBER_NOTIFICATION_TYPE.DISABLE,
        userId: In(userIds),
        conversationId: conversationId,
      },
    });

    return uniq(
      conversationMembers.map(
        (conversationMember) => conversationMember.userId,
      ),
    );
  }

  async getMessageUnreadByConversation(options: {
    userId: string;
    conversationId?: string[];
    parentConversationId?: string[];
  }): Promise<{
    [key: string]: {
      total: number;
      lastReadTime: Date;
    };
  }> {
    const { userId, conversationId, parentConversationId } = options;
    const result = {};

    if (isEmpty(conversationId) && isEmpty(parentConversationId)) {
      return result;
    }

    const unreadQuery = this.conversationMemberRepository
      .createQueryBuilder('conversation_members')
      .select([
        'conversation_members.last_read_time AS last_read_time',
        'COUNT(messages.id) AS total',
      ])
      .leftJoin(
        'messages',
        'messages',
        'messages.conversation_id = conversation_members.conversation_id',
      )
      .where('conversation_members.user_id = :userId', {
        userId: userId,
      })
      .andWhere(
        '(conversation_members.last_read_time < messages.created_at OR conversation_members.last_read_time IS NULL)',
      )
      .andWhere(
        new Brackets((qb) => {
          qb.where('messages.status = :publishedStatus', {
            publishedStatus: MESSAGE_STATUS.PUBLISHED,
          }).orWhere(
            new Brackets((qb2) => {
              qb2
                .where('messages.status = :draftedStatus', {
                  draftedStatus: MESSAGE_STATUS.DRAFTED,
                })
                .andWhere('conversation_members.is_owner = :isOwner', {
                  isOwner: CONVERSATION_MEMBER_TYPE.OWNER,
                });
            }),
          );
        }),
      )
      .groupBy('conversation_members.conversation_id')
      .groupBy('conversation_members.last_read_time');

    if (isNotEmpty(parentConversationId)) {
      unreadQuery
        .addSelect('conversations.parent_id as conversation_id')
        .leftJoin(
          Conversation,
          'conversations',
          'conversations.id = conversation_members.conversation_id',
        )
        .andWhere('conversations.parent_id IN (:...parentConversationIds)', {
          parentConversationIds: [].concat(parentConversationId),
        })
        .addGroupBy('conversations.parent_id');
    }

    if (isNotEmpty(conversationId)) {
      unreadQuery
        .addSelect('conversation_members.conversation_id as conversation_id')
        .andWhere(
          'conversation_members.conversation_id IN (:...conversationIds)',
          {
            conversationIds: [].concat(conversationId),
          },
        )
        .addGroupBy('conversation_members.conversation_id');
    }

    const unread = await unreadQuery
      .orderBy('conversation_members.last_read_time', 'DESC')
      .getRawMany();

    return unread.reduce((acc, item) => {
      acc[item.conversation_id] = {
        total: parseInt(item.total, 10),
        lastReadTime: item?.last_read_time ?? new Date(),
      };
      return acc;
    }, result);
  }

  async findOrCreate(
    conversationId: string,
    userId: string,
    isPublicConversation: boolean = false,
  ): Promise<ConversationMember | boolean> {
    this.logger.debug(
      `[createMemberIfNotExists] conversationId: ${conversationId}, userId: ${userId}`,
    );
    let channel = null;

    channel =
      await this.communityKafka.findOneChannelByConversationId(conversationId);

    if (!channel) {
      throw new ConflictException({
        code: 'CHANNEL_NOT_FOUND',
        message: 'Channel not found',
      });
    }

    const serverMember = await this.communityKafka.findOneMember({
      userId,
      ...(isPublicConversation
        ? {
            serverId: channel.serverId,
          }
        : {
            channelId: channel.id,
          }),
    });

    if (!serverMember) {
      await this.conversationMemberRepository.deleteBy({
        conversationId,
        userId,
      });
      return false;
    }

    const conversationMember =
      await this.conversationMemberRepository.findOneBy({
        conversationId,
        userId,
      });
    const conversationMemberDataUpdate = {
      conversationId,
      userId: serverMember.userId,
      isOwner: serverMember.isOwner,
      memberId: serverMember.id,
      bannedAt: serverMember.bannedAt,
      serverRoleId: serverMember.serverId,
      ...(serverMember?.setting?.notification?.mute !== undefined
        ? {
            notificationType: serverMember.setting.notification.mute
              ? CONVERSATION_MEMBER_NOTIFICATION_TYPE.DISABLE
              : CONVERSATION_MEMBER_NOTIFICATION_TYPE.ALL,
          }
        : {}),
    } as ConversationMember;
    if (conversationMember) {
      await this.conversationMemberRepository.updateBy(
        {
          conversationId,
          userId,
          deletedAt: IsNull(),
        },
        conversationMemberDataUpdate,
      );
      return {
        ...conversationMember,
        ...conversationMemberDataUpdate,
      } as ConversationMember;
    }

    conversationMemberDataUpdate.lastReadTime = moment().toDate();
    conversationMemberDataUpdate.setting = {
      mute: null,
    };
    return await this.conversationMemberRepository.save(
      conversationMemberDataUpdate,
    );
  }
}
