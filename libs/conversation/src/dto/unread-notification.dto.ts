import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { UnreadNotificationCountDto } from '@app/conversation/dto/unread-notification-count.dto';

export class UnreadNotificationDto {
  @Expose()
  @ApiProperty({
    description: 'Conversation ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  conversationId: string;

  @Expose()
  @ApiProperty({
    description: 'Unread notification counts',
    type: UnreadNotificationCountDto,
  })
  notifications: UnreadNotificationCountDto;
}
